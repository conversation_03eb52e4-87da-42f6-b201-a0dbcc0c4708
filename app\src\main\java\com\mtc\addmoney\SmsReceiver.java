package com.mtc.addmoney;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.telephony.SmsMessage;

import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import org.apache.commons.text.StringEscapeUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;

public class SmsReceiver extends BroadcastReceiver {

    private Context context;

    @Override
    public void onReceive(Context context, Intent intent) {
        this.context = context;

        Bundle extras = intent.getExtras();
        if (extras == null) return;

        Object[] pdus = (Object[]) extras.get("pdus");
        if (pdus == null || pdus.length == 0) return;

        SmsMessage[] messages = new SmsMessage[pdus.length];
        StringBuilder fullMessage = new StringBuilder();

        for (int i = 0; i < pdus.length; i++) {
            messages[i] = SmsMessage.createFromPdu((byte[]) pdus[i]);
            fullMessage.append(messages[i].getMessageBody());
        }

        String sender = messages[0].getOriginatingAddress();
        long sentTime = messages[0].getTimestampMillis();
        long receivedTime = System.currentTimeMillis();
        String simInfo = detectSim(extras);

        ForwardingConfig matchedConfig = findMatchingConfig(sender);
        if (matchedConfig == null) return;

        String formattedJson = matchedConfig.getTemplate()
                .replace("%from%", sender)
                .replace("%sentStamp%", String.valueOf(sentTime))
                .replace("%receivedStamp%", String.valueOf(receivedTime))
                .replace("%sim%", simInfo)
                .replace("%text%", Matcher.quoteReplacement(StringEscapeUtils.escapeJson(fullMessage.toString())));

        SystemClock.sleep(1000); // Optional delay

        try {
            sendToWebHook(matchedConfig.getUrl(), formattedJson, matchedConfig.getHeaders(), matchedConfig.getIgnoreSsl(), sender);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ForwardingConfig findMatchingConfig(String sender) {
        List<ForwardingConfig> configs = ForwardingConfig.getAll(context);
        String wildcard = context.getString(R.string.asterisk);

        for (ForwardingConfig config : configs) {
            if (config.getSender().equals(sender) || config.getSender().equals(wildcard)) {
                return config;
            }
        }
        return null;
    }

    private void sendToWebHook(String url, String jsonPayload, String headers, boolean ignoreSsl, String sender) {
        // Create notification log entry
        NotificationLogManager logManager = new NotificationLogManager(context);
        NotificationLog log = logManager.createPendingNotification(url, jsonPayload, sender);

        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        Data data = new Data.Builder()
                .putString(WebHookWorkRequest.DATA_URL, url)
                .putString(WebHookWorkRequest.DATA_TEXT, jsonPayload)
                .putString(WebHookWorkRequest.DATA_HEADERS, headers)
                .putBoolean(WebHookWorkRequest.DATA_IGNORE_SSL, ignoreSsl)
                .putString(WebHookWorkRequest.DATA_LOG_ID, log.getId())
                .putString(WebHookWorkRequest.DATA_SENDER, sender)
                .build();

        OneTimeWorkRequest request = new OneTimeWorkRequest.Builder(WebHookWorkRequest.class)
                .setConstraints(constraints)
                .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, OneTimeWorkRequest.MIN_BACKOFF_MILLIS, TimeUnit.MILLISECONDS)
                .setInputData(data)
                .build();

        WorkManager.getInstance(context).enqueue(request);
    }

    private String detectSim(Bundle extras) {
        // You can customize this method depending on your dual SIM logic
        // Currently just returns a default SIM slot for simplicity

        if (extras.containsKey("simSlot")) {
            return String.valueOf(extras.get("simSlot"));
        }

        // Try common keys for dual SIM detection
        String[] possibleKeys = {"subscription", "simSlot", "slot", "simId", "phone", "slotId"};

        for (String key : possibleKeys) {
            if (extras.containsKey(key)) {
                return String.valueOf(extras.get(key));
            }
        }

        return "unknown";
    }
}
