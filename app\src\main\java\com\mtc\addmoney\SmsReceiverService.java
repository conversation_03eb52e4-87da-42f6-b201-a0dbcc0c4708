package com.mtc.addmoney;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.IBinder;

/* loaded from: classes2.dex */
public class SmsReceiverService extends Service {
    private static final String CHANNEL_ID = "SmsDefault";
    BroadcastReceiver receiver = new SmsReceiver();

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override // android.app.Service
    public void onCreate() {
        super.onCreate();
        IntentFilter intentFilter = new IntentFilter();
        if (Build.VERSION.SDK_INT >= 19) {
            intentFilter.addAction("android.provider.Telephony.SMS_RECEIVED");
        } else {
            intentFilter.addAction("android.provider.Telephony.SMS_RECEIVED");
        }
        registerReceiver(this.receiver, intentFilter);
        if (Build.VERSION.SDK_INT >= 26) {
            ((NotificationManager) getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel(CHANNEL_ID, getText(R.string.notification_channel), 0));
            startForeground(1, new Notification.Builder(this, CHANNEL_ID).setSmallIcon(R.drawable.ic_f).setColor(getColor(R.color.colorPrimary)).setOngoing(true).build());
        }
    }

    @Override // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(this.receiver);
        if (Build.VERSION.SDK_INT >= 26) {
            stopForeground(true);
        }
    }
}