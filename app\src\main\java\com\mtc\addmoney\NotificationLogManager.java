package com.mtc.addmoney;

import android.content.Context;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * Manager class for handling notification logs
 * Provides convenient methods for logging webhook notifications and managing their lifecycle
 */
public class NotificationLogManager {
    
    private static final String TAG = "NotificationLogManager";
    private final Context context;
    
    public NotificationLogManager(Context context) {
        this.context = context;
    }
    
    /**
     * Create a new notification log entry when a webhook is about to be sent
     */
    public NotificationLog createPendingNotification(String webhookUrl, String messageContent, String sender) {
        NotificationLog log = new NotificationLog(context);
        log.setWebhookUrl(webhookUrl);
        log.setMessageContent(messageContent);
        log.setSender(sender);
        log.setStatus(NotificationLog.STATUS_PENDING);
        log.setAttemptCount(1);
        log.save();
        
        Log.d(TAG, "Created pending notification log: " + log.getId());
        return log;
    }
    
    /**
     * Update notification log when webhook request succeeds
     */
    public void markAsSuccess(String logId, int responseCode, String responseMessage) {
        NotificationLog log = findById(logId);
        if (log != null) {
            log.setStatus(NotificationLog.STATUS_SUCCESS);
            log.setResponseCode(responseCode);
            log.setResponseMessage(responseMessage);
            log.save();
            Log.d(TAG, "Marked notification as success: " + logId);
        }
    }
    
    /**
     * Update notification log when webhook request fails
     */
    public void markAsFailed(String logId, int responseCode, String responseMessage) {
        NotificationLog log = findById(logId);
        if (log != null) {
            log.setStatus(NotificationLog.STATUS_FAILED);
            log.setResponseCode(responseCode);
            log.setResponseMessage(responseMessage);
            log.save();
            Log.d(TAG, "Marked notification as failed: " + logId);
        }
    }
    
    /**
     * Update notification log when webhook request is being retried
     */
    public void markAsRetry(String logId, int attemptCount) {
        NotificationLog log = findById(logId);
        if (log != null) {
            log.setStatus(NotificationLog.STATUS_RETRY);
            log.setAttemptCount(attemptCount);
            log.save();
            Log.d(TAG, "Marked notification as retry: " + logId + ", attempt: " + attemptCount);
        }
    }
    
    /**
     * Find a notification log by its ID
     */
    public NotificationLog findById(String logId) {
        List<NotificationLog> allLogs = getAllNotifications();
        for (NotificationLog log : allLogs) {
            if (log.getId().equals(logId)) {
                return log;
            }
        }
        return null;
    }
    
    /**
     * Get all notification logs
     */
    public ArrayList<NotificationLog> getAllNotifications() {
        return NotificationLog.getAll(context);
    }
    
    /**
     * Get notifications filtered by status
     */
    public ArrayList<NotificationLog> getNotificationsByStatus(String status) {
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        ArrayList<NotificationLog> filteredLogs = new ArrayList<>();
        
        for (NotificationLog log : allLogs) {
            if (log.getStatus().equals(status)) {
                filteredLogs.add(log);
            }
        }
        
        return filteredLogs;
    }
    
    /**
     * Get notifications for a specific webhook URL
     */
    public ArrayList<NotificationLog> getNotificationsByUrl(String webhookUrl) {
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        ArrayList<NotificationLog> filteredLogs = new ArrayList<>();
        
        for (NotificationLog log : allLogs) {
            if (webhookUrl.equals(log.getWebhookUrl())) {
                filteredLogs.add(log);
            }
        }
        
        return filteredLogs;
    }
    
    /**
     * Get notifications for a specific sender
     */
    public ArrayList<NotificationLog> getNotificationsBySender(String sender) {
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        ArrayList<NotificationLog> filteredLogs = new ArrayList<>();
        
        for (NotificationLog log : allLogs) {
            if (sender.equals(log.getSender())) {
                filteredLogs.add(log);
            }
        }
        
        return filteredLogs;
    }
    
    /**
     * Get count of notifications by status
     */
    public int getCountByStatus(String status) {
        return getNotificationsByStatus(status).size();
    }
    
    /**
     * Get success rate as percentage
     */
    public double getSuccessRate() {
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        if (allLogs.isEmpty()) {
            return 0.0;
        }
        
        int successCount = getCountByStatus(NotificationLog.STATUS_SUCCESS);
        return (double) successCount / allLogs.size() * 100.0;
    }
    
    /**
     * Delete old notification logs (older than specified days)
     */
    public void cleanupOldLogs(int daysToKeep) {
        long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        
        int deletedCount = 0;
        for (NotificationLog log : allLogs) {
            if (log.getTimestamp() < cutoffTime) {
                log.remove();
                deletedCount++;
            }
        }
        
        Log.d(TAG, "Cleaned up " + deletedCount + " old notification logs");
    }
    
    /**
     * Clear all notification logs
     */
    public void clearAllLogs() {
        NotificationLog.clearAll(context);
        Log.d(TAG, "Cleared all notification logs");
    }
    
    /**
     * Get statistics summary
     */
    public NotificationStats getStats() {
        ArrayList<NotificationLog> allLogs = getAllNotifications();
        
        int totalCount = allLogs.size();
        int successCount = getCountByStatus(NotificationLog.STATUS_SUCCESS);
        int failedCount = getCountByStatus(NotificationLog.STATUS_FAILED);
        int pendingCount = getCountByStatus(NotificationLog.STATUS_PENDING);
        int retryCount = getCountByStatus(NotificationLog.STATUS_RETRY);
        
        return new NotificationStats(totalCount, successCount, failedCount, pendingCount, retryCount);
    }
    
    /**
     * Inner class for notification statistics
     */
    public static class NotificationStats {
        public final int totalCount;
        public final int successCount;
        public final int failedCount;
        public final int pendingCount;
        public final int retryCount;
        public final double successRate;
        
        public NotificationStats(int totalCount, int successCount, int failedCount, int pendingCount, int retryCount) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.pendingCount = pendingCount;
            this.retryCount = retryCount;
            this.successRate = totalCount > 0 ? (double) successCount / totalCount * 100.0 : 0.0;
        }
    }
}
