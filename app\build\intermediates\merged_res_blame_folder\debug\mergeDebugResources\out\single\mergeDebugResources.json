[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_ic_f.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\ic_f.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.mtc.addmoney.app-debug-42:/drawable_green.xml.flat", "source": "com.mtc.addmoney.app-main-44:/drawable/green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_background_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\background_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_root_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\root_gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_item_notification_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\item_notification_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_dialog_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\dialog_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_status_badge_retry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\status_badge_retry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_status_badge_success.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\status_badge_success.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_ic_btn_add.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\ic_btn_add.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_activity_main_with_tabs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\activity_main_with_tabs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "com.mtc.addmoney.app-debug-42:/layout_activity_main.xml.flat", "source": "com.mtc.addmoney.app-main-44:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_fragment_webhooks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\fragment_webhooks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_status_badge_failed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\status_badge_failed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_card_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\card_gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_fragment_monitor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\fragment_monitor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_status_badge_pending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\status_badge_pending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_stats_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\stats_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\layout_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\layout\\list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-debug-42:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mtc.addmoney.app-main-44:\\drawable\\spinner_background.xml"}]