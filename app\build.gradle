plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.mtc.addmoney'
    compileSdk 35

    defaultConfig {
        applicationId "com.mtc.addmoney"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.work.runtime
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation 'org.apache.commons:commons-text:1.10.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.cardview:cardview:1.0.0'

    implementation "androidx.work:work-runtime-ktx:2.9.0"
    implementation 'androidx.viewpager2:viewpager2:1.1.0'
    implementation 'androidx.fragment:fragment:1.6.2'

}