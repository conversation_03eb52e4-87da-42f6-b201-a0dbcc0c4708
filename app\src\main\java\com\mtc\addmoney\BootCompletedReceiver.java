package com.mtc.addmoney;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

public class BootCompletedReceiver extends BroadcastReceiver {

    private static final long START_DELAY_MS = 500;

    @Override
    public void onReceive(final Context context, Intent intent) {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            Intent serviceIntent = new Intent(context, SmsReceiverService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
        }, START_DELAY_MS);
    }
}
