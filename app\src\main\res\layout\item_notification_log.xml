<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Status and Timestamp -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- Status Badge -->
            <TextView
                android:id="@+id/text_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Success"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:background="@drawable/status_badge_success"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginEnd="8dp" />

            <!-- Timestamp -->
            <TextView
                android:id="@+id/text_timestamp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Dec 27, 2024 14:30:25"
                android:textSize="12sp"
                android:textColor="#666666"
                android:gravity="end" />

        </LinearLayout>

        <!-- Sender -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="From: "
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/text_sender"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="+1234567890"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

        <!-- Webhook URL -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="URL: "
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/text_webhook_url"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="https://example.com/webhook"
                android:textSize="12sp"
                android:textColor="#2196F3"
                android:ellipsize="middle"
                android:singleLine="true" />

        </LinearLayout>

        <!-- Response Code and Message (for completed requests) -->
        <LinearLayout
            android:id="@+id/layout_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Response: "
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/text_response_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="200"
                android:textSize="12sp"
                android:textColor="#4CAF50"
                android:textStyle="bold"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/text_response_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="OK"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

        <!-- Attempt Count (for retries) -->
        <LinearLayout
            android:id="@+id/layout_attempts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Attempts: "
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/text_attempt_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

        <!-- Message Content (expandable) -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Message:"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/text_message_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text='{"from":"+1234567890","text":"Hello World","sentStamp":1703686225000}'
            android:textSize="11sp"
            android:textColor="#666666"
            android:typeface="monospace"
            android:background="#F5F5F5"
            android:padding="8dp"
            android:maxLines="3"
            android:ellipsize="end" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
