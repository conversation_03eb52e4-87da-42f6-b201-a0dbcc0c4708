package com.mtc.addmoney.ssl;

import android.net.SSLCertificateSocketFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;

/**
 * A custom SSLSocketFactory that supports TLS for all Android versions.
 * Can be configured to ignore SSL certificate verification.
 */
public class TLSSocketFactory extends SSLSocketFactory {

    private final SSLSocketFactory delegate;

    /**
     * Constructor for TLSSocketFactory.
     *
     * @param ignoreSSLVerification If true, the factory will ignore SSL certificate validation (not secure).
     * @throws NoSuchAlgorithmException if TLS is not supported.
     * @throws KeyManagementException   if there is a problem initializing SSLContext.
     */
    public TLSSocketFactory(boolean ignoreSSLVerification) throws NoSuchAlgorithmException, KeyManagementException {
        if (ignoreSSLVerification) {
            // Insecure factory that ignores SSL certs
            this.delegate = SSLCertificateSocketFactory.getInsecure(0, null);
        } else {
            // Default secure factory
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, null, null);
            this.delegate = sslContext.getSocketFactory();
        }
    }

    @Override
    public String[] getDefaultCipherSuites() {
        return delegate.getDefaultCipherSuites();
    }

    @Override
    public String[] getSupportedCipherSuites() {
        return delegate.getSupportedCipherSuites();
    }

    @Override
    public Socket createSocket() throws IOException {
        return enableTLS(delegate.createSocket());
    }

    @Override
    public Socket createSocket(Socket socket, String host, int port, boolean autoClose) throws IOException {
        return enableTLS(delegate.createSocket(socket, host, port, autoClose));
    }

    @Override
    public Socket createSocket(String host, int port) throws IOException {
        return enableTLS(delegate.createSocket(host, port));
    }

    @Override
    public Socket createSocket(String host, int port, InetAddress localHost, int localPort) throws IOException {
        return enableTLS(delegate.createSocket(host, port, localHost, localPort));
    }

    @Override
    public Socket createSocket(InetAddress host, int port) throws IOException {
        return enableTLS(delegate.createSocket(host, port));
    }

    @Override
    public Socket createSocket(InetAddress address, int port, InetAddress localAddress, int localPort) throws IOException {
        return enableTLS(delegate.createSocket(address, port, localAddress, localPort));
    }

    /**
     * Enables all supported TLS protocols on the given socket.
     *
     * @param socket The socket to configure.
     * @return The same socket with TLS protocols enabled if applicable.
     */
    private Socket enableTLS(Socket socket) {
        if (socket instanceof SSLSocket) {
            SSLSocket sslSocket = (SSLSocket) socket;
            sslSocket.setEnabledProtocols(sslSocket.getSupportedProtocols()); // Enables TLS 1.1, 1.2, 1.3 (if available)
        }
        return socket;
    }
}
