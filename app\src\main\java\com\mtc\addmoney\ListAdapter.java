package com.mtc.addmoney;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import java.util.ArrayList;

public class ListAdapter extends ArrayAdapter<ForwardingConfig> {

    private final Context context;
    private final ArrayList<ForwardingConfig> dataSet;

    public ListAdapter(ArrayList<ForwardingConfig> dataSet, Context context) {
        super(context, R.layout.list_item, dataSet);
        this.context = context;
        this.dataSet = dataSet;
    }

    @Override
    public int getCount() {
        return dataSet.size();
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ForwardingConfig item = getItem(position);

        if (convertView == null) {
            LayoutInflater inflater = LayoutInflater.from(getContext());
            convertView = inflater.inflate(R.layout.list_item, parent, false);
        }

        String sender = item.getSender();
        String wildcard = context.getString(R.string.asterisk);
        String displaySender = sender.equals(wildcard) ? context.getString(R.string.any) : sender;

        TextView senderView = convertView.findViewById(R.id.text_sender);
        TextView urlView = convertView.findViewById(R.id.text_url);
        TextView templateView = convertView.findViewById(R.id.text_template);
        TextView headersView = convertView.findViewById(R.id.text_headers);
        View deleteButton = convertView.findViewById(R.id.delete_button);

        senderView.setText(displaySender);
        urlView.setText(item.getUrl());
        templateView.setText(item.getTemplate());
        headersView.setText(item.getHeaders());

        deleteButton.setTag(R.id.delete_button, position);
        deleteButton.setOnClickListener(this::onDeleteClick);

        return convertView;
    }

    private void onDeleteClick(View view) {
        int position = (Integer) view.getTag(R.id.delete_button);
        ForwardingConfig config = getItem(position);

        String wildcard = context.getString(R.string.asterisk);
        String sender = config.getSender().equals(wildcard) ? context.getString(R.string.any) : config.getSender();

        new AlertDialog.Builder(view.getContext())
                .setTitle(R.string.delete_record)
                .setMessage(String.format(context.getString(R.string.confirm_delete), sender))
                .setPositiveButton(R.string.btn_delete, (dialog, which) -> {
                    remove(config);
                    config.remove();
                })
                .setNegativeButton(R.string.btn_cancel, null)
                .show();
    }
}
