<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="12dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@android:color/transparent"
    android:layout_margin="8dp">

    <LinearLayout
        android:id="@+id/list_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:background="@drawable/gradient_background">

        <!-- Sender Label -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/white"
            android:text="@string/label_sender"
            android:textSize="12sp"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/text_sender"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/text_background"
            android:padding="8dp"
            android:textColor="#212121"
            android:textSize="18sp"
            android:fontFamily="sans-serif" />

        <!-- URL Label -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/label_url"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/text_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/text_background"
            android:padding="8dp"
            android:textColor="#212121"
            android:textSize="18sp"
            android:fontFamily="sans-serif" />

        <!-- JSON Template Label -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/label_json_template"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/text_template"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/text_background"
            android:padding="8dp"
            android:textColor="#212121"
            android:textSize="11sp"
            android:typeface="monospace" />

        <!-- JSON Headers Label -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/label_json_headers"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/text_headers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/text_background"
            android:padding="8dp"
            android:textColor="#212121"
            android:textSize="11sp"
            android:typeface="monospace" />

        <!-- Delete Button -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="12dp">

            <Button
                android:id="@+id/delete_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:minHeight="36dp"
                android:background="@drawable/button_background"
                android:text="@string/btn_delete"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:fontFamily="sans-serif-medium"
                style="@style/Widget.MaterialComponents.Button" />
        </RelativeLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView>