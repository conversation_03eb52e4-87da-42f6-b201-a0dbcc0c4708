<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fadeScrollbars="false">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialog_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/background_gradient"
            android:orientation="vertical"
            android:scrollbars="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="16dp"
                android:text="@string/label_add_sender"
                android:textColor="@color/white" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="4dp"
                app:boxStrokeColor="@color/white"
                app:hintTextAppearance="@style/HintTextAppearance"
                app:hintTextColor="@color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/input_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/hint_sender"
                    android:inputType="text"
                    android:textColorHint="@color/white" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="-4dp"
                android:text="@string/sender_recommendation"
                android:textSize="10sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="16dp"
                android:text="@string/label_url"
                android:textColor="@color/white" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="12dp"
                app:boxStrokeColor="@color/white"
                app:hintTextAppearance="@style/HintTextAppearance"
                app:hintTextColor="@color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/input_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/input_url"
                    android:inputType="textUri"
                    android:textColorHint="@color/white" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:text="@string/label_json_template"
                android:textColor="@color/white" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="4dp"
                app:boxStrokeColor="@color/white"
                app:hintTextAppearance="@style/HintTextAppearance"
                app:hintTextColor="@color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/input_json_template"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="JSON Template"
                    android:inputType="textMultiLine"
                    android:maxLines="10"
                    android:textColorHint="@color/white"
                    android:textSize="11sp"
                    android:typeface="monospace" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="-4dp"
                android:text="@string/json_template_recommendation"
                android:textColor="@color/white"
                android:textSize="11sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="16dp"
                android:text="@string/label_json_headers"
                android:textColor="@color/white" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="4dp"
                app:boxStrokeColor="@color/white"
                app:hintTextAppearance="@style/HintTextAppearance"
                app:hintTextColor="@color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/input_json_headers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="JSON Headers"
                    android:inputType="textMultiLine"
                    android:maxLines="5"
                    android:textColorHint="@color/white"
                    android:textSize="11sp"
                    android:typeface="monospace" />
            </com.google.android.material.textfield.TextInputLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/label_ignore_ssl"
                    android:textColor="@color/white" />

                <CheckBox
                    android:id="@+id/input_ignore_ssl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="20dp" />
            </RelativeLayout>

            <!-- Add Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal"
                android:gravity="end">

                <Button
                    android:id="@+id/btn_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:text="@string/btn_cancel"
                    android:textColor="@color/white"
                    android:background="#2196F3"/>

                <Button
                    android:id="@+id/btn_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/btn_add"
                    android:textColor="@color/white"
                    android:layout_marginRight="9dp"
                    android:background="@color/black" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</ScrollView>