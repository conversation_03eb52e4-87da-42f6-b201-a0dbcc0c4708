<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <permission
        android:name="com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-permission android:name="com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MtcAddMoney"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">


        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- SmsReceiverService -->
        <service
            android:name="com.mtc.addmoney.SmsReceiverService"
            android:enabled="true"
            android:exported="true" />

        <!-- Boot Completed Receiver -->
        <receiver
            android:name="com.mtc.addmoney.BootCompletedReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- AndroidX Startup Providers and WorkManager services -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.mtc.addmoney.androidx-startup">
            <meta-data android:name="androidx.work.WorkManagerInitializer" android:value="androidx.startup" />
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup" />
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup" />
            <meta-data android:name="androidx.profileinstaller.ProfileInstallerInitializer" android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false" />

        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:directBootAware="false" />

        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false"
            android:directBootAware="false" />

        <!-- WorkManager related receivers -->
        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
            android:enabled="true"
            android:exported="false"
            android:directBootAware="false"
            tools:ignore="InnerclassSeparator" />

        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <!-- Optional libraries -->
        <uses-library android:name="androidx.window.extensions" android:required="false" />
        <uses-library android:name="androidx.window.sidecar" android:required="false" />

        <!-- Room multi-instance invalidation service -->
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:exported="false"
            android:directBootAware="true" />

        <!-- Profile Installer Receiver -->
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

    </application>

</manifest>