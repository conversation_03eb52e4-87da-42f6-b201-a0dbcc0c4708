package com.mtc.addmoney;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import java.util.ArrayList;

/**
 * Fragment for managing webhook configurations
 */
public class WebhooksFragment extends Fragment {

    private static final String TAG = "WebhooksFragment";
    private ListView listView;
    private ListAdapter listAdapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView called");
        View view = inflater.inflate(R.layout.fragment_webhooks, container, false);

        listView = view.findViewById(R.id.listView);
        setupListView();

        Log.d(TAG, "onCreateView completed");
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume called - refreshing list to ensure latest data");
        // Refresh the list when the fragment becomes visible to ensure we have the latest data
        if (listAdapter != null) {
            refreshList();
        }
    }

    private void setupListView() {
        Log.d(TAG, "Setting up ListView...");
        ArrayList<ForwardingConfig> configs = ForwardingConfig.getAll(requireContext());
        Log.d(TAG, "Retrieved " + configs.size() + " ForwardingConfig items from storage");

        listAdapter = new ListAdapter(configs, requireContext());
        listView.setAdapter(listAdapter);
        Log.d(TAG, "ListView setup complete with " + listAdapter.getCount() + " items");
    }

    /**
     * Get the list adapter for external access (e.g., from MainActivity)
     */
    public ListAdapter getListAdapter() {
        return listAdapter;
    }

    /**
     * Refresh the webhook list
     */
    public void refreshList() {
        Log.d(TAG, "refreshList() called");
        if (listAdapter != null) {
            Log.d(TAG, "ListAdapter is not null, clearing and reloading data");
            listAdapter.clear();
            ArrayList<ForwardingConfig> configs = ForwardingConfig.getAll(requireContext());
            Log.d(TAG, "Retrieved " + configs.size() + " ForwardingConfig items for refresh");
            listAdapter.addAll(configs);
            listAdapter.notifyDataSetChanged();
            Log.d(TAG, "ListView refreshed with " + listAdapter.getCount() + " items");
        } else {
            Log.w(TAG, "ListAdapter is null, cannot refresh list");
        }
    }
}
